const config = require('config');
//const AppUser = require('../model/appUser');
const { AppError } = require('../util/index');
const GhlPush = require('../model/ghlPush');
const mongoose = require('mongoose');
const Customer = require('../model/customer');
const Permission = require('../model/permissions');
const { DEFAULT_PASSWORD } = require('../constant/enums');
const { getAllUsers, userSearchFeilds } = require('../query/appUser.query');
const { baseListQuery } = require('../query/baseList.query');
const { getGHLDataQuery, ghlserchfields } = require('../query/ghlPush.query');
const AppUser = require('../model/appUser');
const handleServiceError = require('../util/handleError');

class GhlPushService {
    static async getGHLS(params, userLocations) {
        try {
            const status = true;
            let message = 'messageKey.requestCompletedSuccessfully';

            // const { query, pageNum, limit, countQuery } = await baseListQuery(
            //     getGHLDataQuery(params, userLocations),
            //     params,
            //     ghlserchfields,
            //     {},
            // );

            const { query, pageNum, limit, countQuery } = await baseListQuery(
                getGHLDataQuery,       // Pass the function, not the array
                params,
                ghlserchfields,
                {},
                { userLocations }
            );

            const [ghldatass, count] = await Promise.all([
                Customer.aggregate(query),
                Customer.aggregate(countQuery),
            ]);
            const { totalCount } = count[0] || 0;
            if (!totalCount) message = 'messageKey.dataNotFound';
            return {
                status,
                data: ghldatass,
                metaData: {
                    currentPage: pageNum,
                    totalFilteredCount: totalCount || 0,
                    totalFilteredPage: Math.ceil(totalCount / limit) || 1,
                },
                message,
            }
        } catch (error) {
            handleServiceError(error)
        }
    }


}

module.exports = GhlPushService;
