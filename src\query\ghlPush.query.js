// const { default: mongoose } = require("mongoose");

// const getGHLDataQuery = (params, userLocations) => [

//     {
//                 $match: {
//                   ghl_push: true,
//                   ...(params.email ? { email: params.email } : {}) // <- filter by email if provided
//                 }
//               },
//     {
//         $lookup: {
//             from: "customers",
//             localField: "customer_id",
//             foreignField: "customer_id",
//             pipeline: [
//                 {

//                     $match: {
//                         ...(params.location_Id ? { location_Id: new mongoose.Types.ObjectId(params.location_Id) } : userLocations?.length ? {
//                             location_Id: {
//                                 $in: userLocations
//                             }
//                         }
//                             : {}),
//                         ...(params.YTDmin && params.YTDmax
//                             ? {

//                                 "total_expense.total": {
//                                     $gte: Number(params.YTDmin),
//                                     $lte: Number(params.YTDmax)
//                                 },

//                             }
//                             : {}),
//                         ...(params.LifeTimeTotalMin && params.LifeTimeTotalMax
//                             ? {
//                                 "total_expense.life_total": {
//                                     $gte: Number(params.LifeTimeTotalMin),
//                                     $lte: Number(params.LifeTimeTotalMax)
//                                 },
//                             }
//                             : {}),
//                     }
//                 },
//                 {
//                     $lookup: {
//                         from: "locations",
//                         localField: "location_Id",
//                         foreignField: "_id",
//                         as: "location"
//                     }
//                 },
//                 {
//                     $unwind: {
//                         path: "$location"
//                     }
//                 }
//             ],
//             as: "customerDetail"
//         }
//     },
//     {
//         $unwind: {
//             path: "$customerDetail"
//         }
//     },
//     {
//         $project: {
//             _id: 1,
//             first: '$customerDetail.first',
//             last: '$customerDetail.last',
//             email: '$customerDetail.email',
//             phone: '$customerDetail.phone',
//             club: '$customerDetail.location.location',
//             total_expense: '$customerDetail.total_expense'
//         }
//     }
// ];

// const ghlserchfields = [
//     '_id',
//     'first',
//     'last',
//     'email',
//     'phone',
//     'club',
//     'total_expense'
// ];

// module.exports = {
//     getGHLDataQuery,
//     ghlserchfields
// }

const { default: mongoose } = require("mongoose");

const getGHLDataQuery = (params, userLocations, skip = 0, limit = 100) => [
  {
    $match: {
      ghl_push: true,
      ...(params.email
        ? {
          $or: [
            { first: { $regex: params.email, $options: "i" } },
            { last: { $regex: params.email, $options: "i" } },
            { email: { $regex: params.email, $options: "i" } }
          ]
        }
        : {})
    }
  },

  {
    $lookup: {
      from: "customers",
      localField: "customer_id",
      foreignField: "customer_id",
      pipeline: [
        {
          $match: {
            ...(params.location_Id
              ? { location_Id: new mongoose.Types.ObjectId(params.location_Id) }
              : userLocations?.length
                ? { location_Id: { $in: userLocations } }
                : {}
            ),
            ...(params.YTDmin && params.YTDmax
              ? { "total_expense.total": { $gte: Number(params.YTDmin), $lte: Number(params.YTDmax) } }
              : {}
            ),
            ...(params.gPlusBadge == "true"
              ? { "total_expense.total": { $ne: 0 } }
              : params.gPlusBadge == "false"
                ? { "total_expense.total": 0 }
                : {}
            )
          }
        },
        { $lookup: { from: "locations", localField: "location_Id", foreignField: "_id", as: "location" } },
        { $unwind: { path: "$location", preserveNullAndEmptyArrays: true } }
      ],
      as: "customerDetail"
    }
  },
  { $unwind: "$customerDetail" }, // This removes docs without matching customers
  {
    $project: {
      _id: 1,
      first: "$customerDetail.first",
      last: "$customerDetail.last",
      email: "$customerDetail.email",
      phone: "$customerDetail.phone",
      club: "$customerDetail.location.location",
      total_expense: "$customerDetail.total_expense"
    }
  },
  { $sort: { _id: -1 } },
  { $skip: skip },   // ✅ Move pagination after lookups
  { $limit: limit }  // ✅ Move pagination after lookups
];



const ghlserchfields = [
  "_id",
  "first",
  "last",
  "email",
  "phone",
  "club",
  "total_expense"
];

module.exports = {
  getGHLDataQuery,
  ghlserchfields
};
