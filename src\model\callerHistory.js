const mongoose = require('mongoose');
const { Schema } = mongoose;
const { SERVICE_STATUS } = require('../constant/enums');

const callerHistorySchema = new Schema(
    {
        userId: {
            type: Schema.Types.ObjectId,
            ref: 'AppUser',
            required: true,
        },
        leadId: {
            type: Schema.Types.ObjectId,
            ref: 'Lead',
            required: true,
        },
        reason: {
            type: String
        },
        notes: {
            type: String
        },
        action: {
            type: String,
            enum: SERVICE_STATUS,
            default: 'NEEDS_ACTION'
        }
    },
    {
        timestamps: true,
    }
);

const CallerHistory = mongoose.model('CallerHistory', callerHistorySchema);
module.exports = CallerHistory