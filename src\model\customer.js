const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const totalExpenseSchema = new Schema({
    service: { type: Number, default: 0 },
    membership: { type: Number, default: 0 },
    product: { type: Number, default: 0 },
    gift: { type: Number, default: 0 },
    total: { type: Number, default: 0 },
    life_total: { type: Number, default: 0 },
    tgs_total: { type: Number, default: 0 },
    tgs_life_total: { type: Number, default: 0 },
}, { _id: false });

const customerschema = new Schema({
    customer_id: { type: String },
    first: { type: String, required: true },
    last: { type: String, required: true },
    email: { type: String, required: true },
    phone: { type: String, default: null },
    tenant_id: {
        type: String,
        required: true
    },
    meevo_location_id: {
        type: String,
        required: true
    },
    location_Id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Location'
    },
    GuardianId: { type: String },
    isMinor: { type: Boolean },
    city: { type: String },
    State: { type: String },
    zipCode: { type: String },
    country: { type: String },
    total_expense: totalExpenseSchema,
    ghl_push: { type: Boolean, default: false }
}, {
    timestamps: true
});

const Customer = mongoose.model('customer', customerschema);

module.exports = Customer;