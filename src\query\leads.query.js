const { default: mongoose } = require("mongoose");
const { ServiceStatus } = require("../constant/enums");

// const getLeadsQuery = (params, userLocations, skip = 0, limit = 100) => [
//     {
//         $match: {
//             ...(params.location_Id ? { location_Id: new mongoose.Types.ObjectId(params.location_Id) } : userLocations?.length ? {
//                 location_Id: {
//                     $in: userLocations.map(id => new mongoose.Types.ObjectId(id)),
//                 },
//             }
//                 : {}),
//             // ...(userLocations?.length
//             //     ? {
//             //         location_Id: {
//             //             $in: userLocations.map(id => new mongoose.Types.ObjectId(id)),
//             //         },
//             //     }
//             //     : {}),
//             ...(params.service_status ? { service_status: params.service_status } : {}),
//             ...(params.tag_id ? { tag_id: Number(params.tag_id) } : {}),
//             ...(params.dateFrom && params.dateEnd
//                 ? {
//                     createdAt: {
//                         $gte: new Date(params.dateFrom),
//                         $lte: new Date(params.dateEnd),
//                     }
//                 }
//                 : params.dateFrom
//                     ? {
//                         createdAt: {
//                             $gte: new Date(params.dateFrom),
//                         }
//                     }
//                     : {})
//         }
//     },
//     {
//         $lookup: {
//             from: "sources",
//             localField: "source",
//             foreignField: "_id",
//             as: "source"
//         }
//     },
//     {
//         $lookup: {
//             from: 'callerhistories',
//             let: { leadId: '$_id' },
//             pipeline: [
//                 {
//                     $match: {
//                         $expr: {
//                             $eq: ['$leadId', '$$leadId']
//                         }
//                     }
//                 },
//                 {
//                     $lookup: {
//                         from: "appusers",
//                         localField: "userId",
//                         foreignField: "_id",
//                         as: "appuser"
//                     }
//                 },
//                 {
//                     $unwind: { path: "$appuser" }
//                 },
//                 {
//                     $addFields: {
//                         caller: {
//                             $concat: [
//                                 "$appuser.firstName",
//                                 " ",
//                                 "$appuser.lastName"
//                             ]
//                         }
//                     },
//                 },
//                 { $sort: { createdAt: -1 } },
//                 { $limit: 10 },
//                 {
//                     $project: {
//                         reason: 1,
//                         notes: 1,
//                         createdAt: 1,
//                         action: 1,
//                         caller: 1,

//                     },
//                 },
//             ],
//             as: 'callHistory',
//         },
//     },
//     {
//         $lookup: {
//             from: 'locations',
//             localField: 'location_Id',
//             foreignField: '_id',
//             as: 'locationDetails',
//         },
//     },
//     {
//         $addFields: {
//             location: {
//                 $ifNull: [{ $arrayElemAt: ["$locationDetails", 0] }, {}]
//             },
//             sourceName: {
//                 $ifNull: [
//                     {
//                         $getField: {
//                             field: "sourceName",
//                             input: {
//                                 $arrayElemAt: ["$source", 0]
//                             }
//                         }
//                     },
//                     ""
//                 ]
//             }
//         }
//     },
//     {
//         $project: {
//             _id: 1,
//             firstname: 1,
//             lastname: 1,
//             email: 1,
//             phone: 1,
//             tagId: 1,
//             location: 1,
//             tag_id: 1,
//             source: 1,
//             service_status: 1,
//             UTM_details: 1,
//             callHistory: 1,
//             createdAt: 1
//         }
//     },
//     { $skip: skip },
//     { $limit: limit }
// ];


const getLeadsQuery = (params, userLocations, skip = 0, limit = 100) => {
    const sortOrder = params.sortOrder?.toLowerCase() === 'desc' ? -1 : 1;
    const allowedSortFields = ['firstname', 'lastname', 'createdAt', 'email', 'phone'];
    const sortField = allowedSortFields.includes(params.sortField) ? params.sortField : 'createdAt';

    return [
        {
            $match: {
                ...(params.location_Id
                    ? { location_Id: new mongoose.Types.ObjectId(params.location_Id) }
                    : userLocations?.length
                        ? { location_Id: { $in: userLocations.map(id => new mongoose.Types.ObjectId(id)) } }
                        : {}),
                ...(params.service_status ? { service_status: params.service_status } : {}),
                ...(params.tag_id ? { tag_id: Number(params.tag_id) } : {}),
                ...(params.source_Id ? { source: new mongoose.Types.ObjectId(params.source_Id) } : {}),
                ...(params.dateFrom && params.dateEnd
                    ? {
                        createdAt: {
                            $gte: new Date(params.dateFrom),
                            $lte: new Date(params.dateEnd),
                        }
                    }
                    : params.dateFrom
                        ? { createdAt: { $gte: new Date(params.dateFrom) } }
                        : {})
            }
        },
        {
            $lookup: {
                from: "sources",
                localField: "source",
                foreignField: "_id",
                as: "source"
            }
        },
        // {
        //     $unwind: {
        //         path: "$source",
        //         preserveNullAndEmptyArrays: true
        //     }
        // },
        {
            $lookup: {
                from: 'callerhistories',
                let: { leadId: '$_id' },
                pipeline: [
                    { $match: { $expr: { $eq: ['$leadId', '$$leadId'] } } },
                    {
                        $lookup: {
                            from: "appusers",
                            localField: "userId",
                            foreignField: "_id",
                            as: "appuser"
                        }
                    },
                    { $unwind: { path: "$appuser" } },
                    {
                        $addFields: {
                            caller: {
                                $concat: [
                                    "$appuser.firstName",
                                    " ",
                                    "$appuser.lastName"
                                ]
                            }
                        },
                    },
                    { $sort: { createdAt: -1 } },
                    { $limit: 10 },
                    {
                        $project: {
                            reason: 1,
                            notes: 1,
                            createdAt: 1,
                            action: 1,
                            caller: 1,
                        },
                    },
                ],
                as: 'callHistory',
            },
        },
        {
            $lookup: {
                from: 'locations',
                localField: 'location_Id',
                foreignField: '_id',
                as: 'locationDetails',
            },
        },
        {
            $addFields: {
                location: {
                    $ifNull: [{ $arrayElemAt: ["$locationDetails", 0] }, {}]
                },
                sourceName: {
                    $ifNull: [
                        {
                            $getField: {
                                field: "sourceName",
                                input: { $arrayElemAt: ["$source", 0] }
                            }
                        },
                        ""
                    ]
                }
            }
        },
        {
            $project: {
                _id: 1,
                firstname: 1,
                lastname: 1,
                email: 1,
                phone: 1,
                tagId: 1,
                location: 1,
                tag_id: 1,
                source: 1,
                service_status: 1,
                UTM_details: 1,
                callHistory: 1,
                createdAt: 1
            }
        },
        { $sort: { [sortField]: sortOrder } }, // <-- sorting here
        { $skip: skip },                       // <-- pagination
        { $limit: limit }                      // <-- pagination
    ];
};


const leadSearchFields = [
    "_id",
    "firstname",
    "lastname",
    "email",
    "phone",
    "tagId",
    "location",
    "tag_id",
    "source",
    "service_status",
    "UTM_details",
    "callHistory",
    "createdAt"
]

module.exports = {
    getLeadsQuery,
    leadSearchFields
}