const mongoose = require('mongoose');
const config = require('config');

async function addLeadIndexes() {
    try {
        // Connect to MongoDB
        const MONGODB_URI = config.get('DB_URL');
        await mongoose.connect(MONGODB_URI);
        console.log('Connected to MongoDB');

        const db = mongoose.connection.db;

        // Add indexes to leads collection
        console.log('Adding indexes to leads collection...');
        
        // Compound index for location filtering and date sorting (most important)
        await db.collection('leads').createIndex(
            { location_Id: 1, createdAt: -1 },
            { background: true, name: 'location_createdAt_idx' }
        );
        console.log('✓ Added location_Id + createdAt compound index');

        // Individual field indexes
        await db.collection('leads').createIndex(
            { service_status: 1 },
            { background: true, name: 'service_status_idx' }
        );
        console.log('✓ Added service_status index');

        await db.collection('leads').createIndex(
            { source: 1 },
            { background: true, name: 'source_idx' }
        );
        console.log('✓ Added source index');

        await db.collection('leads').createIndex(
            { tag_id: 1 },
            { background: true, name: 'tag_id_idx' }
        );
        console.log('✓ Added tag_id index');

        await db.collection('leads').createIndex(
            { createdAt: -1 },
            { background: true, name: 'createdAt_idx' }
        );
        console.log('✓ Added createdAt index');

        await db.collection('leads').createIndex(
            { firstname: 1 },
            { background: true, name: 'firstname_idx' }
        );
        console.log('✓ Added firstname index');

        await db.collection('leads').createIndex(
            { lastname: 1 },
            { background: true, name: 'lastname_idx' }
        );
        console.log('✓ Added lastname index');

        await db.collection('leads').createIndex(
            { email: 1 },
            { background: true, name: 'email_idx' }
        );
        console.log('✓ Added email index');

        await db.collection('leads').createIndex(
            { phone: 1 },
            { background: true, name: 'phone_idx' }
        );
        console.log('✓ Added phone index');

        // Add indexes to callerhistories collection
        console.log('Adding indexes to callerhistories collection...');
        
        await db.collection('callerhistories').createIndex(
            { leadId: 1, createdAt: -1 },
            { background: true, name: 'leadId_createdAt_idx' }
        );
        console.log('✓ Added leadId + createdAt compound index');

        await db.collection('callerhistories').createIndex(
            { userId: 1 },
            { background: true, name: 'userId_idx' }
        );
        console.log('✓ Added userId index');

        // Add indexes to sources collection
        console.log('Adding indexes to sources collection...');
        
        await db.collection('sources').createIndex(
            { _id: 1, sourceName: 1 },
            { background: true, name: 'id_sourceName_idx' }
        );
        console.log('✓ Added _id + sourceName compound index');

        // Add indexes to locations collection
        console.log('Adding indexes to locations collection...');
        
        await db.collection('locations').createIndex(
            { _id: 1, location: 1 },
            { background: true, name: 'id_location_idx' }
        );
        console.log('✓ Added _id + location compound index');

        // Add indexes to appusers collection
        console.log('Adding indexes to appusers collection...');
        
        await db.collection('appusers').createIndex(
            { _id: 1, firstName: 1, lastName: 1 },
            { background: true, name: 'id_name_idx' }
        );
        console.log('✓ Added _id + firstName + lastName compound index');

        console.log('\n🎉 All indexes added successfully!');
        console.log('\nTo verify indexes were created, run:');
        console.log('db.leads.getIndexes()');
        console.log('db.callerhistories.getIndexes()');
        console.log('db.sources.getIndexes()');
        console.log('db.locations.getIndexes()');
        console.log('db.appusers.getIndexes()');

    } catch (error) {
        console.error('Error adding indexes:', error);
    } finally {
        await mongoose.disconnect();
        console.log('Disconnected from MongoDB');
    }
}

// Run the script
if (require.main === module) {
    addLeadIndexes();
}

module.exports = addLeadIndexes;
