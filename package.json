{"name": "better_csi_ms", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "cross-env NODE_ENV=dev node src/server/server.js", "dev": "cross-env NODE_ENV=dev nodemon src/server/server.js", "tgp_start": "cross-env NODE_ENV=tgp node src/server/server.js", "demo_start": "cross-env NODE_ENV=demo node src/server/server.js", "wp_start": "cross-env NODE_ENV=wp node src/server/server.js", "add-indexes": "cross-env NODE_ENV=dev node scripts/add-lead-indexes.js", "test-performance": "cross-env NODE_ENV=dev node scripts/test-lead-performance.js", "add-indexes:tgp": "cross-env NODE_ENV=tgp node scripts/add-lead-indexes.js", "test-performance:tgp": "cross-env NODE_ENV=tgp node scripts/test-lead-performance.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.4", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "config": "^3.3.12", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.7", "express": "^4.21.2", "fs": "^0.0.1-security", "handlebars": "^4.7.8", "json2csv": "^6.0.0-alpha.2", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "mongoose": "^8.9.7", "nodemailer": "^6.10.1", "nodemon": "^3.1.9", "yup": "^1.6.1"}, "devDependencies": {"cross-env": "^7.0.3"}}