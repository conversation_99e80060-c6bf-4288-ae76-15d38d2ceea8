const mongoose = require('mongoose');

const ghlPushSchema = new mongoose.Schema({
    ghl_id: { type: String, required: true },
    customer_id: { type: String, required: true },
    customerLocalId: { type: mongoose.Types.ObjectId, ref: 'highmasters', required: true },
    locationName: { type: String },
    locationLocalId: { type: mongoose.Types.ObjectId, ref: 'locations', required: true },
    annual_total: { type: Number, default: 0 },
    lifetime_total: { type: Number, default: 0 }
},
    {
        timestamps: true
    });

const GhlPush = mongoose.model('ghlmasters', ghlPushSchema);
module.exports = GhlPush;
